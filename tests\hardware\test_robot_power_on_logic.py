# tests/hardware/test_robot_power_on_logic.py
"""
测试机器人上电逻辑的模拟版本
不需要实际硬件连接，用于验证状态转换逻辑
"""

import sys
import os

# 将项目根目录添加到Python路径中
PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../'))
sys.path.insert(0, PROJECT_ROOT)

class MockNRCInterface:
    """模拟的NRC接口，用于测试状态转换逻辑"""
    
    def __init__(self):
        self.servo_state = 0  # 初始状态：停止
        self.error_cleared = False
        self.power_on_called = False
        self.power_off_called = False
        self.servo_state_set = False
        
    def get_servo_state(self, socket_fd, state_ref):
        """模拟获取伺服状态"""
        return 0, self.servo_state  # 返回码, 状态值
        
    def clear_servo_error(self, socket_fd):
        """模拟清除伺服错误"""
        self.error_cleared = True
        if self.servo_state == 2:  # 如果是报警状态，清错后变为停止状态
            self.servo_state = 0
        return 0  # 成功
        
    def set_servo_state(self, socket_fd, state):
        """模拟设置伺服状态"""
        self.servo_state_set = True
        if state == 1 and self.servo_state == 0:  # 从停止到就绪
            self.servo_state = 1
            return 0
        return 0
        
    def set_servo_poweron(self, socket_fd):
        """模拟伺服上电"""
        self.power_on_called = True
        if self.servo_state == 1:  # 只有就绪状态才能上电
            self.servo_state = 3  # 上电后变为运行状态
            return 0
        return -1  # 失败
        
    def set_servo_poweroff(self, socket_fd):
        """模拟伺服下电"""
        self.power_off_called = True
        if self.servo_state == 3:  # 从运行状态下电
            self.servo_state = 1  # 下电后变为就绪状态
            return 0
        return -1  # 失败

class MockRobotStatusReader:
    """模拟的机器人状态读取器"""
    
    def __init__(self, initial_state=0):
        self.nrc = MockNRCInterface()
        self.nrc.servo_state = initial_state
        self.socket_fd = 1  # 模拟socket描述符
        
    def _safe_get_value(self, func, *args, default_value=None):
        """安全地调用SDK函数并提取返回值"""
        try:
            if len(args) == 1 and isinstance(args[0], int):
                result = func(self.socket_fd, args[0])
                if isinstance(result, tuple) and len(result) > 1:
                    return result[0], result[1]  # 返回码, 实际值
                return result, args[0]
            else:
                result = func(self.socket_fd, *args)
                if isinstance(result, tuple) and len(result) > 1:
                    return result[0], result[1]  # 返回码, 实际值
                return result, default_value
        except Exception as e:
            return -1, f"错误: {e}"

    def robot_initialize_and_power_on(self):
        """
        完整的机器人初始化和上电流程
        根据INEXBOT文档实现正确的状态转换逻辑
        """
        import time
        
        print("开始机器人初始化和上电流程...")
        
        # 定义状态名称映射
        servo_names = {0: "停止状态", 1: "就绪状态", 2: "报警状态", 3: "运行状态"}
        
        try:
            # 第1步：无论如何，先清除可能存在的历史错误
            print("步骤 1: 清除错误...")
            result = self.nrc.clear_servo_error(self.socket_fd)
            if result != 0:
                print(f"警告：清除错误返回码: {result}")
            else:
                print("清除错误成功")
            
            # 延时一小段时间，等待控制器响应
            time.sleep(0.1)  # 缩短延时用于测试

            # 第2步：获取当前伺服状态，以决定下一步操作
            ret_code, current_state = self._safe_get_value(self.nrc.get_servo_state, 0)
            if ret_code != 0:
                print(f"错误：无法获取伺服状态！返回码: {ret_code}")
                return False
            
            print(f"步骤 2: 获取到当前伺服状态为 {current_state} ({servo_names.get(current_state, '未知状态')})")

            # 第3步：根据当前状态，执行不同的逻辑分支，最终目标是进入状态1（就绪）
            if current_state == 2:  # 报警状态
                print("状态2 (报警): 清错后需要重新检查状态...")
                # 再次尝试清错
                result = self.nrc.clear_servo_error(self.socket_fd)
                time.sleep(0.1)
                ret_code, current_state = self._safe_get_value(self.nrc.get_servo_state, 0)
                if current_state == 2:
                    print("错误：清错后仍处于报警状态，可能存在持续的硬件错误")
                    return False
                print(f"清错后状态变为: {current_state} ({servo_names.get(current_state, '未知状态')})")
            
            elif current_state == 3:  # 运行状态
                print("状态3 (运行): 检测到机器人已在运行，现在执行下电操作以进入就绪态...")
                result = self.nrc.set_servo_poweroff(self.socket_fd)
                if result != 0:
                    print(f"错误：从运行状态下电失败！返回码: {result}")
                    return False
                time.sleep(0.1)  # 等待下电完成
                print("下电完成，机器人应进入状态1。")
                
                # 验证下电结果
                ret_code, current_state = self._safe_get_value(self.nrc.get_servo_state, 0)
                print(f"下电后状态: {current_state} ({servo_names.get(current_state, '未知状态')})")

            elif current_state == 0:  # 停止状态
                print("状态0 (停止): 准备设置伺服为就绪状态...")
                result = self.nrc.set_servo_state(self.socket_fd, 1)  # 设置为就绪状态
                if result != 0:
                    print(f"错误：从停止状态设置伺服就绪失败！返回码: {result}")
                    return False
                time.sleep(0.1)
                print("设置伺服就绪成功，机器人应进入状态1。")
                
                # 验证设置结果
                ret_code, current_state = self._safe_get_value(self.nrc.get_servo_state, 0)
                print(f"设置后状态: {current_state} ({servo_names.get(current_state, '未知状态')})")

            elif current_state == 1:  # 已经是就绪状态
                print("状态1 (就绪): 机器人已处于就绪状态，可以直接上电。")

            # 经过以上步骤，机器人状态理论上应该为 1 (就绪)
            # 再次确认一下
            ret_code, final_state = self._safe_get_value(self.nrc.get_servo_state, 0)
            if final_state != 1:
                print(f"错误：未能成功进入就绪状态，当前状态为 {final_state} ({servo_names.get(final_state, '未知状态')})")
                return False
            
            print("步骤 3: 机器人已处于状态1 (就绪)。")

            # 第4步：根据文档要求，上电前需要先调用 set_servo_state(1)
            print("步骤 4: 上电前设置伺服状态...")
            result = self.nrc.set_servo_state(self.socket_fd, 1)
            if result != 0:
                print(f"警告：上电前设置伺服状态返回码: {result}")
            time.sleep(0.1)

            # 第5步：执行上电
            print("步骤 5: 执行上电操作...")
            result = self.nrc.set_servo_poweron(self.socket_fd)
            if result != 0:
                print(f"错误：上电失败！返回码: {result}")
                print("请检查：")
                print("  - 安全回路是否正常")
                print("  - 示教器是否设置为远程模式")
                print("  - 物理控制器是否正常")
                print("  - 急停按钮是否释放")
                return False

            # 第6步：最后确认是否进入运行状态
            time.sleep(0.1)  # 等待上电完成
            ret_code, final_state = self._safe_get_value(self.nrc.get_servo_state, 0)
            if final_state == 3:
                print(f"成功！机器人已上电，当前状态为 {final_state} ({servo_names.get(final_state, '未知状态')})。")
                print("✅ 机器人现在可以执行运动控制指令了！")
                return True
            else:
                print(f"错误：上电后状态异常，当前状态为 {final_state} ({servo_names.get(final_state, '未知状态')})。")
                return False

        except Exception as e:
            print(f"初始化和上电流程失败: {e}")
            import traceback
            traceback.print_exc()
            return False

def test_power_on_from_different_states():
    """测试从不同初始状态的上电流程"""
    
    print("=" * 60)
    print("机器人上电逻辑测试（模拟版本）")
    print("=" * 60)
    
    # 测试场景1：从停止状态开始
    print("\n【测试场景1：从停止状态(0)开始】")
    reader1 = MockRobotStatusReader(initial_state=0)
    success1 = reader1.robot_initialize_and_power_on()
    print(f"结果：{'成功' if success1 else '失败'}")
    
    # 测试场景2：从就绪状态开始
    print("\n【测试场景2：从就绪状态(1)开始】")
    reader2 = MockRobotStatusReader(initial_state=1)
    success2 = reader2.robot_initialize_and_power_on()
    print(f"结果：{'成功' if success2 else '失败'}")
    
    # 测试场景3：从报警状态开始
    print("\n【测试场景3：从报警状态(2)开始】")
    reader3 = MockRobotStatusReader(initial_state=2)
    success3 = reader3.robot_initialize_and_power_on()
    print(f"结果：{'成功' if success3 else '失败'}")
    
    # 测试场景4：从运行状态开始
    print("\n【测试场景4：从运行状态(3)开始】")
    reader4 = MockRobotStatusReader(initial_state=3)
    success4 = reader4.robot_initialize_and_power_on()
    print(f"结果：{'成功' if success4 else '失败'}")
    
    print("\n" + "=" * 60)
    print("测试总结：")
    print(f"  停止状态 -> 运行状态: {'✅' if success1 else '❌'}")
    print(f"  就绪状态 -> 运行状态: {'✅' if success2 else '❌'}")
    print(f"  报警状态 -> 运行状态: {'✅' if success3 else '❌'}")
    print(f"  运行状态 -> 运行状态: {'✅' if success4 else '❌'}")
    print("=" * 60)

if __name__ == "__main__":
    test_power_on_from_different_states()
